# Stage 1: Build
FROM eclipse-temurin:21-jdk AS build

WORKDIR /build

COPY gradlew gradlew.bat ./
COPY gradle/ gradle/
COPY settings.gradle build.gradle ./

COPY hrm-common/ hrm-common/
COPY src/ src/

RUN chmod +x gradlew
RUN ./gradlew build -x test --no-daemon

# Stage 2: Run
FROM eclipse-temurin:21-jre-jammy

WORKDIR /app

RUN groupadd -r hrm && useradd -r -g hrm hrm
COPY --from=build /build/build/libs/*.jar app.jar
RUN chown hrm:hrm app.jar

USER hrm
EXPOSE 6020

HEALTHCHECK --interval=30s --timeout=3s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:6020/test || exit 1

ENTRYPOINT ["java", "-jar", "app.jar"]
