package com.gianghp.hrm_hr_service.controllers;


import com.gianghp.hrm.dtos.EmployeeBasicDto;
import com.gianghp.hrm_hr_service.producers.KafkaHrProducer;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.UUID;

@RestController
@RequestMapping("/test")
@RequiredArgsConstructor
public class TestController {

    private final KafkaHrProducer kafkaHrProducer;

    @GetMapping
    public String hello() {
        EmployeeBasicDto employee = new EmployeeBasicDto();
        employee.setId(new UUID(1,1));
        employee.setEmployeeCode("testCode");
        employee.setFullName("testName");
        employee.setEmail("<EMAIL>");
        employee.setDesignationName("test");
        employee.setDepartmentName("test");

        kafkaHrProducer.sendEmployee<PERSON>hen<PERSON>reated(employee);
        return "Sent employee created event";
    }
}
