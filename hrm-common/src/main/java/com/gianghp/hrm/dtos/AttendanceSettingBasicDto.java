package com.gianghp.hrm.dtos;

import com.gianghp.hrm.constants.NumericConstants;
import com.gianghp.hrm.enums.WorkType;
import jakarta.persistence.Column;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class AttendanceSettingBasicDto {

  private UUID id;

  private int minWorkingMinutes; // ví dụ: 240 phút (4 giờ)

  private BigDecimal standardWorkingHours; // ví dụ: 480 phút (8 giờ)

  private WorkType workType;

  private LocalDate effectiveDate;
}
