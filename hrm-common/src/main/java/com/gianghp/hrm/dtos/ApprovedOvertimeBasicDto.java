package com.gianghp.hrm.dtos;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.UUID;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ApprovedOvertimeBasicDto {
  private UUID id;
  private UUID employeeId;
  private LocalDate date;
  private BigDecimal totalHours;
  private BigDecimal rate;
}
