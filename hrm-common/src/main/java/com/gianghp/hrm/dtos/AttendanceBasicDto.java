package com.gianghp.hrm.dtos;

import com.gianghp.hrm.constants.NumericConstants;
import com.gianghp.hrm.enums.CheckinStatus;
import com.gianghp.hrm.enums.CheckoutStatus;
import jakarta.persistence.Column;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
public class AttendanceBasicDto {
  private UUID id;

  private UUID employeeId;

  private LocalDate date;

  private CheckinStatus checkinStatus;

  private CheckoutStatus checkoutStatus;

  private BigDecimal standardWorkingHours;

  private BigDecimal overtimeHours;

  private BigDecimal totalLateDeductionRate;

  private BigDecimal totalEarlyOutDeductionRate;

  private BigDecimal overtimeRate;

}
