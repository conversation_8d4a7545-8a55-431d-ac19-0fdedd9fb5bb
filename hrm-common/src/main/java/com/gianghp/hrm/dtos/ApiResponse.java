package com.gianghp.hrm.dtos;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder; // <-- Thêm import này

import java.time.LocalDateTime;
import java.util.List; // Thêm import này nếu bạn muốn ví dụ List<T>

/**
 * Generic API Response DTO - có thể dùng chung cho tất cả các service
 * Đã được cập nhật để hỗ trợ thông tin phân trang.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class ApiResponse<T> {
    private boolean success;
    private String message;
    private T data;
    private LocalDateTime timestamp;


    private Long totalElements;
    private Integer totalPages;
    private Integer pageNumber;

    public ApiResponse(boolean success, String message, T data) {
        this.success = success;
        this.message = message;
        this.data = data;
        this.timestamp = LocalDateTime.now();
        this.totalElements = null;
        this.totalPages = null;
        this.pageNumber = null;
    }

    public ApiResponse(boolean success, String message, T data, Long totalElements, Integer totalPages, Integer pageNumber) {
        this.success = success;
        this.message = message;
        this.data = data;
        this.timestamp = LocalDateTime.now();
        this.totalElements = totalElements;
        this.totalPages = totalPages;
        this.pageNumber = pageNumber;
    }


    public static <T> ApiResponse<T> success(T data) {
        return new ApiResponse<>(true, "Success", data, null, null, null); // Không có info phân trang
    }

    public static <T> ApiResponse<T> success(String message, T data) {
        return new ApiResponse<>(true, message, data, null, null, null); // Không có info phân trang
    }

    public static <T> ApiResponse<T> error(String message) {
        return new ApiResponse<>(false, message, null, null, null, null); // Không có data và info phân trang
    }

    public static <T> ApiResponse<T> error(String message, T data) {
        return new ApiResponse<>(false, message, data, null, null, null); // Không có info phân trang
    }

    public static <T> ApiResponse<T> success(String message, T data, Long totalElements, Integer totalPages, Integer pageNumber) {
        return new ApiResponse<>(true, message, data, totalElements, totalPages, pageNumber);
    }
}