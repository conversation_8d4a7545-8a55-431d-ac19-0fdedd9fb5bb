package com.gianghp.hrm.utils;

import com.gianghp.hrm.enums.DayType;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.DayOfWeek;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.List;

/**
 * Utility class cho xử lý ngày tháng - có thể dùng chung cho tất cả các service
 */
public final class DateTimeUtils {
    
    public static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    public static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    private DateTimeUtils() {
        // Utility class - không cho phép khởi tạo
    }
    
    /**
     * Format LocalDate to string
     */
    public static String formatDate(LocalDate date) {
        return date != null ? date.format(DATE_FORMATTER) : null;
    }
    
    /**
     * Format LocalDateTime to string
     */
    public static String formatDateTime(LocalDateTime dateTime) {
        return dateTime != null ? dateTime.format(DATETIME_FORMATTER) : null;
    }
    
    /**
     * Parse string to LocalDate
     */
    public static LocalDate parseDate(String dateString) {
        return dateString != null ? LocalDate.parse(dateString, DATE_FORMATTER) : null;
    }
    
    /**
     * Parse string to LocalDateTime
     */
    public static LocalDateTime parseDateTime(String dateTimeString) {
        return dateTimeString != null ? LocalDateTime.parse(dateTimeString, DATETIME_FORMATTER) : null;
    }
    
    /**
     * Calculate days between two dates
     */
    public static long daysBetween(LocalDate startDate, LocalDate endDate) {
        return ChronoUnit.DAYS.between(startDate, endDate);
    }
    
    /**
     * Check if date is in the past
     */
    public static boolean isInPast(LocalDate date) {
        return date != null && date.isBefore(LocalDate.now());
    }
    
    /**
     * Check if date is in the future
     */
    public static boolean isInFuture(LocalDate date) {
        return date != null && date.isAfter(LocalDate.now());
    }
    
    /**
     * Get current year
     */
    public static int getCurrentYear() {
        return LocalDate.now().getYear();
    }

    public static BigDecimal calculateHoursBetween(LocalTime start, LocalTime end) {
        Duration duration = Duration.between(start, end);

        // Nếu end < start (qua nửa đêm), thì cộng 1 ngày (24h)
        if (duration.isNegative()) {
            duration = duration.plusDays(1);
        }

        long seconds = duration.getSeconds();
        BigDecimal hours = BigDecimal.valueOf(seconds)
                .divide(BigDecimal.valueOf(3600), 2, RoundingMode.HALF_UP);

        return hours;
    }

    public static int calculateMinutesBetween(LocalTime start, LocalTime end) {
        Duration duration = Duration.between(start, end);
        if (duration.isNegative()) {
            duration = duration.plusDays(1);
        }
        return (int) duration.toMinutes();
    }

    public static DayType getDayType(LocalDate date, List<LocalDate> holidays) {
        if (holidays != null && holidays.contains(date)) {
            return DayType.HOLIDAY;
        }

        DayOfWeek dayOfWeek = date.getDayOfWeek();
        if (dayOfWeek == DayOfWeek.SATURDAY || dayOfWeek == DayOfWeek.SUNDAY) {
            return DayType.WEEKEND;
        }

        return DayType.WEEKDAY;
    }

    public static int calculateWorkingDaysInMonth(YearMonth month) {
        int workingDays = 0;
        LocalDate firstDay = month.atDay(1);
        LocalDate lastDay = month.atEndOfMonth();

        for (LocalDate date = firstDay; !date.isAfter(lastDay); date = date.plusDays(1)) {
            DayOfWeek day = date.getDayOfWeek();
            if (day != DayOfWeek.SATURDAY && day != DayOfWeek.SUNDAY) {
                workingDays++;
            }
        }

        return workingDays;
    }

}
