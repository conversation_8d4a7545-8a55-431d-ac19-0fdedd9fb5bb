package com.gianghp.hrm.configs;

import io.jsonwebtoken.security.Keys;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import javax.crypto.SecretKey;
import java.nio.charset.StandardCharsets;

/**
 * JWT Configuration dùng chung cho tất cả microservices
 * Chỉ auth-service tạo token, các service khác chỉ validate
 */
@Getter
@Configuration
public class JwtConfig {

    /**
     * -- GETTER --
     *  Secret string (chỉ dùng cho debug, không expose ra ngoài)
     */
    @Value("${jwt.secret}")
    private String secret;

    /**
     * -- GETTER --
     *  Thời gian hết hạn token (milliseconds)
     */
    @Value("${jwt.expiration}") // 24 hours default
    private long expiration;

    /**
     * Lấy secret key để sign/verify JWT
     */
    public SecretKey getKey() {
        // Đ<PERSON><PERSON> bảo secret key đủ dài cho HS256 (ít nhất 32 bytes)
        if (secret.length() < 32) {
            secret = secret + "0".repeat(32 - secret.length());
        }
        return Keys.hmacShaKeyFor(secret.getBytes(StandardCharsets.UTF_8));
    }

}
