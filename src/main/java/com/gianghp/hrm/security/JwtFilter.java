package com.gianghp.hrm.security;

import io.jsonwebtoken.Claims;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * JWT Filter tối ưu cho microservices
 * Không cần UserDetailsService, tạo UserDetails từ token
 * Hiệu quả và nhanh chóng
 */
@Component
@Slf4j
public class JwtFilter extends OncePerRequestFilter {

    private final JwtService jwtService;

    public JwtFilter(JwtService jwtService) {
        this.jwtService = jwtService;
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request,
                                    HttpServletResponse response,
                                    FilterChain filterChain) throws ServletException, IOException {

        final String authHeader = request.getHeader("Authorization");
        final String jwtToken;

        // 1. Kiểm tra Header Authorization
        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            filterChain.doFilter(request, response);
            return;
        }

        try {
            // 2. Trích xuất Token
            jwtToken = authHeader.substring(7);

            log.info("JWT token: {}", jwtToken);
            log.info("Token valid: {}", jwtService.isTokenValid(jwtToken));

            // 3. Validate token (chỉ kiểm tra signature và expiration)
            if (jwtService.isTokenValid(jwtToken) && SecurityContextHolder.getContext().getAuthentication() == null) {
                // 4. Tạo UserDetails từ token (không cần database)
                UserDetails userDetails = createUserDetailsFromToken(jwtToken);

                // 5. Thiết lập authentication
                UsernamePasswordAuthenticationToken authToken = new UsernamePasswordAuthenticationToken(
                        userDetails, null, userDetails.getAuthorities());
                authToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                SecurityContextHolder.getContext().setAuthentication(authToken);
            }
        } catch (Exception e) {
            // Log error nhưng không block request
            logger.debug("JWT validation failed: " + e.getMessage());
        }

        // 6. Chuyển tiếp request
        filterChain.doFilter(request, response);
    }

    /**
     * Tạo UserDetails từ JWT token (không cần database)
     * Hiệu quả cho microservices
     */
    private UserDetails createUserDetailsFromToken(String token) {
        Claims claims = jwtService.extractAllClaims(token);
        String username = claims.get("username", String.class);
        String role = claims.get("role", String.class);

        // Tạo authorities từ role
        List<GrantedAuthority> authorities = new ArrayList<>();
        if (role != null) {
            authorities.add(new SimpleGrantedAuthority("ROLE_" + role));
        }

        // Tạo UserDetails với thông tin cơ bản
        return new User(username, "", authorities);
    }
}
