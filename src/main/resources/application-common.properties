# Swagger UI dùng chung
springdoc.api-docs.path=/v3/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.swagger-ui.operations-sorter=alpha
springdoc.swagger-ui.tags-sorter=alpha
springdoc.swagger-ui.doc-expansion=none


spring.jackson.property-naming-strategy=SNAKE_CASE

logging.level.org.springframework.security=DEBUG
spring.jpa.properties.hibernate.show_sql=true
spring.jpa.properties.hibernate.format_sql=true
logging.level.org.hibernate.type.descriptor.sql=TRACE

# JWT dùng chung
jwt.expiration=864000000
jwt.secret=mySecretKey123456789012345678901234567890

# Kafka bootstrap
spring.kafka.bootstrap-servers=localhost:9092

# SASL/PLAIN authentication
spring.kafka.properties.security.protocol=SASL_PLAINTEXT
spring.kafka.properties.sasl.mechanism=PLAIN
spring.kafka.properties.sasl.jaas.config=org.apache.kafka.common.security.plain.PlainLoginModule required username="gianghp" password="gianghp";

# Producer (g?i JSON)
spring.kafka.producer.key-serializer=org.apache.kafka.common.serialization.StringSerializer
spring.kafka.producer.value-serializer=org.springframework.kafka.support.serializer.JsonSerializer

# Consumer (nh?n JSON)
spring.kafka.consumer.key-deserializer=org.apache.kafka.common.serialization.StringDeserializer
spring.kafka.consumer.value-deserializer=org.springframework.kafka.support.serializer.JsonDeserializer
spring.kafka.consumer.properties.spring.json.trusted.packages=*

spring.kafka.consumer.auto-offset-reset=earliest



